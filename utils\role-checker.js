/**
 * 角色检查工具类
 * 提供统一的用户角色检查功能
 */

const utils = require('./util');
const config = require('../config/common');

/**
 * 角色检查器
 */
class RoleChecker {
  
  /**
   * 检查用户是否具有指定角色
   * @param {string} roleId - 角色ID
   * @returns {Promise<boolean>} 是否具有该角色
   */
  static checkUserRole(roleId) {
    return new Promise((resolve, reject) => {
      utils.getStorage('userInfo').then(res => {
        if (!res || !res.Roles) {
          reject(false);
          return;
        }
        
        const hasRole = res.Roles.find(item => {
          return item.RoleId === roleId;
        });
        
        if (hasRole) {
          resolve(true);
        } else {
          reject(false);
        }
      }).catch(err => {
        console.error('获取用户信息失败:', err);
        reject(false);
      });
    });
  }

  /**
   * 检查用户是否为现场代表
   * @returns {Promise<boolean>} 是否为现场代表
   */
  static isXianChangDaiBiao() {
    return this.checkUserRole(config.XCDBRoleId);
  }

  /**
   * 检查用户是否为司机
   * @returns {Promise<boolean>} 是否为司机
   */
  static isSiJi() {
    return this.checkUserRole(config.SJRoleId);
  }

  /**
   * 检查用户是否为维修主管
   * @returns {Promise<boolean>} 是否为维修主管
   */
  static isWeiXiuZhuGuan() {
    return this.checkUserRole(config.WXZGRoleId);
  }

  /**
   * 检查用户是否为车队长
   * @returns {Promise<boolean>} 是否为车队长
   */
  static isCheDouZhang() {
    return this.checkUserRole(config.CDJRoleId);
  }

  /**
   * 检查用户是否为搅拌车管理
   * @returns {Promise<boolean>} 是否为搅拌车管理
   */
  static isJiaoBanCheGuanLi() {
    return this.checkUserRole(config.JBCRoleId);
  }

  /**
   * 检查用户是否为泵车管理
   * @returns {Promise<boolean>} 是否为泵车管理
   */
  static isBengCheGuanLi() {
    return this.checkUserRole(config.BCRoleId);
  }

  /**
   * 获取用户的最高角色ID
   * @returns {Promise<string>} 最高角色ID
   */
  static getUserMaxRole() {
    return new Promise((resolve, reject) => {
      utils.getStorage('userInfo').then(res => {
        if (!res || !res.Roles) {
          reject('无用户角色信息');
          return;
        }
        
        let tempMaxRole = 0;
        res.Roles.forEach((item) => {
          if (tempMaxRole <= parseInt(item.RoleId)) {
            tempMaxRole = parseInt(item.RoleId);
          }
        });
        
        resolve(tempMaxRole.toString());
      }).catch(err => {
        console.error('获取用户信息失败:', err);
        reject(err);
      });
    });
  }

  /**
   * 获取用户的所有角色信息
   * @returns {Promise<Array>} 角色数组
   */
  static getUserRoles() {
    return new Promise((resolve, reject) => {
      utils.getStorage('userInfo').then(res => {
        if (!res || !res.Roles) {
          reject('无用户角色信息');
          return;
        }
        
        resolve(res.Roles);
      }).catch(err => {
        console.error('获取用户信息失败:', err);
        reject(err);
      });
    });
  }

  /**
   * 检查用户是否具有多个角色中的任意一个
   * @param {Array<string>} roleIds - 角色ID数组
   * @returns {Promise<boolean>} 是否具有任意一个角色
   */
  static hasAnyRole(roleIds) {
    return new Promise((resolve, reject) => {
      utils.getStorage('userInfo').then(res => {
        if (!res || !res.Roles) {
          reject(false);
          return;
        }
        
        const hasAnyRole = res.Roles.some(userRole => {
          return roleIds.includes(userRole.RoleId);
        });
        
        if (hasAnyRole) {
          resolve(true);
        } else {
          reject(false);
        }
      }).catch(err => {
        console.error('获取用户信息失败:', err);
        reject(false);
      });
    });
  }

  /**
   * 获取角色名称映射
   * @returns {Object} 角色ID到角色名称的映射
   */
  static getRoleNameMapping() {
    return {
      [config.SJRoleId]: '司机',
      [config.JBCRoleId]: '搅拌车管理',
      [config.BCRoleId]: '泵车管理',
      [config.CDJRoleId]: '车队长',
      [config.WXZGRoleId]: '维修主管',
      [config.WXRoleId]: '维修人员',
      [config.AllRoleId]: '维修查看（所有车辆）',
      [config.XCDBRoleId]: '现场代表'
    };
  }

  /**
   * 根据角色ID获取角色名称
   * @param {string} roleId - 角色ID
   * @returns {string} 角色名称
   */
  static getRoleName(roleId) {
    const mapping = this.getRoleNameMapping();
    return mapping[roleId] || '未知角色';
  }
}

module.exports = RoleChecker;
