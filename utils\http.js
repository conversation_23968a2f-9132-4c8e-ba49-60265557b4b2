const utils = require('../utils/util');
const config = require('../config/common');

const httpRequest = (url, {
    method = "POST",
    header = {},
    data = {}
}) => {
    return new Promise((resolve, reject) => {
        wx.showLoading({
            title: '加载中',
        })
        //请求url
        url = config.requestUrlHost + url;
        console.log('url',url);
        console.log('data',data);
        //读取缓存中的openId
        utils.getStorage('OpenId')
            .then(
                bigres => {
                    data.sign = "9060f36b431794263f5b3ec17d137f81";
                    data.timestamp = "1688354049772.72";
                    data.openId = bigres;
                    utils.getStorage('userInfo')
                        .then(
                            userres => {
                                data.UserId = userres.UserId;
                                userres.Roles.forEach(item=>{
                                    if(item.RoleId==config.SJRoleId)
                                    {
                                        data.RolesId=config.SJRoleId;
                                    }
                                    else if(item.RoleId==config.XCDBRoleId)
                                    {
                                        data.RolesId=config.XCDBRoleId;
                                    }
                                })
                                console.log('data',data);
                                    wx.request({
                                        url,
                                        method,
                                        header,
                                        data,
                                        success: res => {
                                            // utils.getStorage('Cookie').then(res => {
                                            // }).catch(err => {
                                            //     utils.setStorage('Cookie', res.header['Set-Cookie']);
                                            // })
                                            console.log('res.',res);
                                            resolve(res);
                                        },
                                        fail: err => {
                                            reject(err);
                                        },
                                        complete: () => {
                                            wx.hideLoading();//隐藏掉刚刚的showloading  
                                        }
                                    })
                            }
                        ).catch(err => { 
                            wx.request({
                                url,
                                method,
                                header,
                                data,
                                success: res => {
                                    console.log('res..',res);
                                    resolve(res);
                                },
                                fail: err => {
                                    console.log('err',err);
                                    reject(err);
                                },
                                complete: () => {
                                    wx.hideLoading();//隐藏掉刚刚的showloading  
                                }
                            })
                        })
                }).catch(
                    err => {
                        wx.request({
                            url,
                            method,
                            header,
                            data,
                            success: res => {
                                console.log('res',res);
                                resolve(res);
                            },
                            fail: err => {
                                console.log('请求失败', err);
                                reject(err);
                            },
                            complete: () => {
                                wx.hideLoading();//隐藏掉刚刚的showloading  
                            }
                        })
                    }
                )
    })
}

module.exports = httpRequest